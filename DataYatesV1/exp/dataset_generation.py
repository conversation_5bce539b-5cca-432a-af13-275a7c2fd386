
def generate_gaborium_dataset(exp, ks_results, roi_src, pix_interp, ep_interp, valid_interp, dt=1/240, metadata={}):
    protocols = get_trial_protocols(exp)
    ptb2ephys, _ = get_clock_functions(exp)
    
    st = ks_results.spike_times
    clu = ks_results.spike_clusters
    cids = np.unique(clu)

    # Export Gaborium dataset
    gaborium_trials = [(iT, GaboriumTrial(exp['D'][iT], exp['S'])) for iT in range(len(exp['D'])) if protocols[iT] == 'ForageGabor']
    print(f'There are {len(gaborium_trials)} Gaborium trials.')

    gaborium_dict = {
        't_bins': [],
        'trial_inds': [],
        'stim': [],
        'robs': [],
        'dpi_pix': [],
        'eyepos': [],
        'dpi_valid': [],
        'roi': [],
    }
    for iT, trial in tqdm(gaborium_trials, 'Regenerating Gaborium Stimulus'):
        # get flip times in ephys time
        flip_times = ptb2ephys(trial.flip_times)

        # Setup bins
        trial_bin_edges = np.arange(flip_times[0], flip_times[-1], dt)
        trial_bins = trial_bin_edges[:-1] + dt/2
        gaborium_dict['t_bins'].append(trial_bins)
        gaborium_dict['trial_inds'].append(np.ones_like(trial_bins) * iT)

        # Get DPI
        trial_dpi = pix_interp(trial_bins)
        gaborium_dict['dpi_pix'].append(trial_dpi)
        gaborium_dict['eyepos'].append(ep_interp(trial_bins))
        gaborium_dict['dpi_valid'].append(valid_interp(trial_bins))

        # Get ROI
        trial_roi = trial_dpi[...,None].astype(int) + roi_src[None,...]
        gaborium_dict['roi'].append(trial_roi)

        # Get the frame index for each bin 
        frame_inds = np.searchsorted(flip_times, trial_bins) - 1
        frame_inds[frame_inds < 0] = 0

        # Sample the stimulus for each frame
        trial_stim = trial.get_frames(frame_inds, roi=trial_roi)
        gaborium_dict['stim'].append(trial_stim)

        # Bin spikes
        trial_robs = bin_spikes(st, trial_bin_edges, clu, cids)
        gaborium_dict['robs'].append(trial_robs)

    if gaborium_trials:
        for k, v in gaborium_dict.items():
            gaborium_dict[k] = np.concatenate(v)

        return DictDataset(gaborium_dict, metadata=metadata)
    else:
        return None

def generate_gratings_dataset(exp, ks_results, roi_src, 
                              pix_interp, ep_interp, valid_interp, dt=1/240, 
                              metadata={}):
    protocols = get_trial_protocols(exp)
    ptb2ephys, _ = get_clock_functions(exp)
    st = ks_results.spike_times
    clu = ks_results.spike_clusters
    cids = np.unique(clu)

    # Export Gratings dataset
    trials = [(iT, GratingsTrial(exp['D'][iT], exp['S'])) for iT in range(len(exp['D'])) if protocols[iT] == 'ForageGrating']
    print(f'There are {len(trials)} Gratings trials.')
    gratings_dict = {
            't_bins': [],
            'stim': [],
            'stim_phase': [],
            'sf': [],
            'ori': [],
            'robs': [],
            'dpi_pix': [],
            'eyepos': [],
            'dpi_valid': [],
            'roi': [],
            'trial_inds': [],
        }
    for iT, trial in tqdm(trials, 'Generating Gratings dataset'):
        # get flip times in ephys time
        flip_times = ptb2ephys(trial.flip_times)

        # Setup bins
        trial_bin_edges = np.arange(flip_times[0], flip_times[-1], dt)
        trial_bins = trial_bin_edges[:-1] + dt/2
        gratings_dict['t_bins'].append(trial_bins)
        gratings_dict['trial_inds'].append(np.ones_like(trial_bins) * iT)

        # Get DPI
        trial_dpi = pix_interp(trial_bins)
        gratings_dict['dpi_pix'].append(trial_dpi)
        gratings_dict['eyepos'].append(ep_interp(trial_bins))
        gratings_dict['dpi_valid'].append(valid_interp(trial_bins))

        # Get ROI
        trial_roi = trial_dpi[...,None].astype(int) + roi_src[None,...]
        gratings_dict['roi'].append(trial_roi)

        # Get the frame index for each bin 
        frame_inds = np.searchsorted(flip_times, trial_bins) - 1
        frame_inds[frame_inds < 0] = 0

        # Sample the stimulus for each frame
        trial_stim = trial.get_frames(frame_inds, roi=trial_roi)
        gratings_dict['stim'].append(trial_stim)
        trial_stim_phase = trial.get_frames_phase(frame_inds, roi=trial_roi)
        gratings_dict['stim_phase'].append(trial_stim_phase)

        # Get the spatial frequency and orientation for each frame
        trial_sf = trial.spatial_frequencies[frame_inds]
        gratings_dict['sf'].append(trial_sf)
        trial_ori = trial.orientations[frame_inds]
        gratings_dict['ori'].append(trial_ori)

        # Bin spikes
        trial_robs = bin_spikes(st, trial_bin_edges, clu, cids)
        gratings_dict['robs'].append(trial_robs)

    if trials:
        for k, v in gratings_dict.items():
            gratings_dict[k] = np.concatenate(v)

        return DictDataset(gratings_dict, metadata=metadata)
    else:
        return None

def generate_backimage_dataset(exp, ks_results, roi_src, pix_interp, ep_interp, valid_interp, dt=1/240, metadata={}):
    protocols = get_trial_protocols(exp)
    ptb2ephys, _ = get_clock_functions(exp)
    
    st = ks_results.spike_times
    clu = ks_results.spike_clusters
    cids = np.unique(clu)

    # Export BackImage dataset
    backimage_trials = [(iT, BackImageTrial(exp['D'][iT], exp['S'])) for iT in range(len(exp['D'])) if protocols[iT] == 'BackImage']
    print(f'There are {len(backimage_trials)} BackImage trials.')

    backimage_dict = {
        't_bins': [],
        'trial_inds': [],
        'stim': [],
        'robs': [],
        'dpi_pix': [],
        'eyepos' : [],
        'dpi_valid': [],
        'roi': [],
    }
    for iT, trial in tqdm(backimage_trials, 'BackImage trials'):
        # Setup bins
        trial_onset = ptb2ephys(trial.image_onset_ptb)
        trial_offset = ptb2ephys(trial.image_offset_ptb)
        trial_bin_edges = np.arange(trial_onset, trial_offset, dt)
        trial_bins = trial_bin_edges[:-1] + dt/2
        backimage_dict['t_bins'].append(trial_bins)
        backimage_dict['trial_inds'].append(np.ones_like(trial_bins, dtype=np.int64) * iT)

        # Get DPI
        trial_dpi = pix_interp(trial_bins)
        trial_dpi_valid = valid_interp(trial_bins)
        backimage_dict['dpi_pix'].append(trial_dpi)
        backimage_dict['eyepos'].append(ep_interp(trial_bins))
        backimage_dict['dpi_valid'].append(trial_dpi_valid)

        # Get ROI
        trial_roi = trial_dpi[...,None].astype(int) + roi_src[None,...]
        backimage_dict['roi'].append(trial_roi)

        # Sample the stimulus for each frame
        trial_stim = trial.get_roi(trial_roi)
        backimage_dict['stim'].append(trial_stim)

        # Bin spikes
        robs_trial = bin_spikes(st, trial_bin_edges, clu, cids)
        backimage_dict['robs'].append(robs_trial)

    if backimage_trials:
        for k, v in backimage_dict.items():
            backimage_dict[k] = np.concatenate(v)

        return DictDataset(backimage_dict, metadata=metadata)
    else:
        return None